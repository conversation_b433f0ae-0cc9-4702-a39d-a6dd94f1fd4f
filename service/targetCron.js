const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");

const indexName = config.INDEX.targetCron;

async function getData(userId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ createdAt: { order: "desc" } }],
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }],
        },
      },
      size: 1
    },
  });

  return response.body?.hits?.hits[0]?._source || null;
}

module.exports = {
  insertData,
  getData,
}
