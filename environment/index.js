const nconf = require("nconf");
const _ = require('lodash');

nconf
    .argv()
    .env()
    .file({file: __dirname + '/env/local.env.json'});

var all = {
    env: nconf.get('NODE_ENV') || 'development',
    INDEX: {
        water: "water",
        activity: "activity", activitySummary: "activity_summary",
        sleep: "sleep",
        bp: "bp", bg: "bg", egvs: "egvs", spo2: "spo2",
        heartRate: "heart_rate", restingHeartRate: "resting_heart_rate", hrv: "hrv",
        vo2: "vo2", ecg: "ecg", 
        height: "height", weight: "weight", fat: "fat", bmi: "bmi",
        temp: "temp", waistSize: "waist_size", hipSize: "hip_size",
        chestSize: "chest_size", armSize: "arm_size", quadSize: "quad_size",
        mindfulness: "mindfulness", 

        exerciseLogs: "exercise_logs",
        mindfulnessLogs: "mindfulness_logs",
        
        trackers: "trackers", targets: "user_targets", targets_achieved: "user_targets_achievements",
        targetCron: "target_cron",
        userProfiles: "user_profiles",

        recommendations: "user_recommendations",
        chatInteractions: "chat-memory",
    },
    exerciseTargetIds: ["10", "29", "33"],
    mindfulnessTargetIds: ["11", "34", "35"],
    foodTypes: {
        solid: "solid",
        beverage: "beverage",
        fats: 'fats',
        cheese: 'cheese'
    },
};

const config = _.merge(
    all,
    require('./' + all.env + '.js') || {});

module.exports = { config };
